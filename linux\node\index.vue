<template>
	<page-container ref="pageContainer" title="节点管理">
		<view class="node-container">
			<!-- 模糊遮罩 -->
			<view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

			<!-- 悬浮上下文菜单 -->
			<view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
				<!-- 编辑和删除选项仅对非本机节点可用 -->
				<template v-if="currentNode && !currentNode.isLocal">
					<view class="menu-item" @click="handleAccessNode">
						<uni-icons type="redo" size="16" color="#4CD964"></uni-icons>
						<text class="menu-text">访问</text>
					</view>
					<!-- <view class="menu-item" @click="handleEditNode">
						<uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
						<text class="menu-text">编辑</text>
					</view> -->
					<view class="menu-divider"></view>
					<view class="menu-item menu-delete" @click="showDeleteDialog = true">
						<uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
						<text class="menu-text menu-text-delete">删除</text>
					</view>
				</template>

				<!-- 本机节点的提示信息 -->
				<template v-else-if="currentNode && currentNode.isLocal">
					<view class="menu-divider"></view>
					<view class="menu-item menu-disabled">
						<uni-icons type="info" size="16" color="#999"></uni-icons>
						<text class="menu-text menu-text-disabled">本机节点不可操作</text>
					</view>
				</template>
			</view>

			<!-- 克隆项容器 -->
			<view class="fixed-clone-container" v-if="showContextMenu">
				<view :style="clonePosition" v-if="currentNode">
					<view class="node-item-clone bg-primary">
						<view class="node-header">
							<view class="node-name-container">
								<view class="node-name">{{ currentNode.name }}</view>
								<!-- 本机节点标识 -->
								<view v-if="currentNode.isLocal" class="local-node-badge">
									<text class="local-node-text">本机</text>
								</view>
							</view>
							<view :class="['node-status-badge', currentNode.status]">
								<view class="status-dot"></view>
								<text>{{ currentNode.status === 'online' ? '在线' : '离线' }}</text>
							</view>
						</view>
						<view class="node-info">
							<view class="info-row">
								<text class="info-label">地址:</text>
								<text class="info-value">{{ currentNode.address }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">CPU:</text>
								<text class="info-value">{{ currentNode.cpu }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">内存:</text>
								<text class="info-value">{{ currentNode.memory }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">分类:</text>
								<text class="info-value">{{ currentNode.category }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 节点列表 -->
			<z-paging
				ref="nodePaging"
				class="mt-170"
				:default-page-size="10"
				use-virtual-list
				:force-close-inner-list="true"
				:auto-hide-loading-after-first-loaded="false"
				:auto-show-system-loading="true"
				@query="queryNodes"
				@virtualListChange="virtualListChange"
				@refresherStatusChange="reload"
				:refresher-complete-delay="200"
			>
				<view
					class="px-20"
					v-for="(node, index) in nodesList"
					:id="`zp-id-${node.zp_index}`"
					:key="node.zp_index"
				>
					<view
						class="node-item-container bg-primary mt-20"
						@touchstart="handleTouchStart($event)"
						@touchmove="handleTouchMove($event)"
						@touchend="handleTouchEnd($event)"
						@touchcancel="handleTouchCancel($event)"
						:data-index="index"
						:data-node="JSON.stringify(node)"
					>
						<view class="node-header">
							<view class="node-name-container">
								<view class="node-name">{{ node.name }}</view>
								<!-- 本机节点标识 -->
								<view v-if="node.isLocal" class="local-node-badge">
									<text class="local-node-text">本机</text>
								</view>
							</view>
							<view :class="['node-status-badge', node.status]">
								<view class="status-dot"></view>
								<text>{{ node.status === 'online' ? '在线' : '离线' }}</text>
							</view>
						</view>
						<view class="node-info">
							<view class="info-row">
								<text class="info-label">地址:</text>
								<text class="info-value">{{ node.address }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">CPU:</text>
								<text class="info-value">{{ node.cpu }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">内存:</text>
								<text class="info-value">{{ node.memory }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">分类:</text>
								<text class="info-value">{{ node.category }}</text>
							</view>
						</view>
					</view>
				</view>
			</z-paging>

			<!-- 删除确认对话框 -->
			<CustomDialog
				contentHeight="200rpx"
				v-model="showDeleteDialog"
				title="确认删除"
				:confirmText="'删除'"
				:confirmStyle="{
					backgroundColor: '#FF3B30',
				}"
				@confirm="confirmDeleteNode"
			>
				<view class="text-secondary flex justify-center items-center h-full">
					确定要删除节点 "{{ currentNode?.name }}" 吗？
				</view>
			</CustomDialog>
		</view>
	</page-container>
</template>

<script setup>
	import { onBackPress, onShow, onUnload } from '@dcloudio/uni-app';
	import { watch, ref } from 'vue';
	import {
		loading,
		currentNode,
		currentNodeIndex,
		nodePaging,
		pageContainer,
		showContextMenu,
		showDeleteDialog,
		menuPosition,
		clonePosition,
		loadNodes,
		handleEditNode,
		handleAccessNode,
		confirmDeleteNode,
		hideContextMenu,
		handleTouchStart,
		handleTouchMove,
		handleTouchEnd,
		handleTouchCancel,
		getNodeList,
		reload,
	} from './useController';
	import PageContainer from '@/components/PageContainer/index.vue';
	import CustomDialog from '@/components/CustomDialog/index.vue';

	const nodesList = ref([]);

	const virtualListChange = (vList) => {
		nodesList.value = vList;
	};

	/**
	 * 查询节点列表
	 */
	const queryNodes = async (page, pageSize) => {
		try {
			const res = await getNodeList(page, pageSize);
			nodePaging.value.complete(res);
			nodePaging.value.updateVirtualListRender();
		} catch (error) {
			console.log(error);
			nodePaging.value.complete([]);
		}
	};

	// 监听加载状态
	watch(loading, (newVal) => {
		if (newVal) {
			uni.showLoading({
				title: '加载中...',
			});
		} else {
			uni.hideLoading();
		}
	});

	// 初始化
	onShow(async () => {
		// 加载节点数据
		await loadNodes();
	});

	onBackPress(() => {
		// 如果长按菜单正在显示，先关闭菜单
		if (showContextMenu.value) {
			hideContextMenu();
			return true;
		}
		return false;
	});

	onUnload(() => {
		showDeleteDialog.value = false;
	});
</script>

<style lang="scss" scoped>
	.node-container {
		background-color: var(--bg-color);
	}

	/* 模糊遮罩 */
	.blur-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		backdrop-filter: blur(4px);
		background-color: rgba(0, 0, 0, 0.15);
		z-index: 900;
		transition: all 0.2s ease;
	}

	/* 上下文菜单 */
	.context-menu {
		position: fixed;
		background-color: #ffffff;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.12);
		z-index: 999;

		// 水平位置类
		&.menu-left {
			transform: translateX(0);
		}

		&.menu-right {
			transform: translateX(0);
		}

		&.menu-center {
			transform: translateX(-50%);
		}

		&.menu-middle {
			margin-top: 0;
		}

		.menu-item {
			padding: 24rpx;
			display: flex;
			align-items: center;
			transition: all 0.2s ease;

			&:active {
				background-color: #f5f5f5;
				transform: scale(0.98);
			}

			.menu-text {
				margin-left: 16rpx;
				font-size: 28rpx;
				color: #333;
				font-weight: 500;
			}

			&.menu-delete {
				.menu-text-delete {
					color: #ff3b30;
				}
				&:active {
					background-color: rgba(255, 59, 48, 0.1);
				}
			}

			&.menu-disabled {
				opacity: 0.6;
				pointer-events: none;

				.menu-text-disabled {
					color: #999;
				}
			}
		}

		.menu-divider {
			height: 1rpx;
			background-color: #f0f0f0;
			margin: 0 24rpx;
		}
	}

	/* 克隆容器 */
	.fixed-clone-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		pointer-events: none;
		z-index: 901;
	}

	.node-item-clone {
		padding: 30rpx;
		opacity: 0.9;
		border-radius: 14rpx;
	}

	/* 节点项样式 */
	.node-item-container {
		padding: 30rpx;
		border-radius: 14rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.3);
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
		transition: all 0.2s ease;

		&:active {
			background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
			transform: scale(0.98);
		}
	}

	.node-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 16rpx;
		padding-bottom: 12rpx;
		border-bottom: 1px solid var(--border-color);
	}

	.node-name-container {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.node-name {
		font-size: 32rpx;
		font-weight: bold;
		color: var(--text-color-primary, #333);
	}

	.local-node-badge {
		background-color: #f59e0b;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.local-node-text {
			font-size: 20rpx;
			color: #ffffff;
			font-weight: 500;
			line-height: 1;
		}
	}

	.node-status-badge {
		display: inline-flex;
		align-items: center;
		padding: 8rpx 16rpx;
		font-size: 24rpx;
		font-weight: 500;
		border-radius: 20rpx;
		color: #ffffff;

		&.online {
			background-color: #22c55e;
		}
		&.offline {
			background-color: #ef4444;
		}

		.status-dot {
			width: 12rpx;
			height: 12rpx;
			border-radius: 50%;
			background-color: #ffffff;
			margin-right: 8rpx;
		}
	}

	.node-info {
		display: flex;
		flex-direction: column;
		gap: 12rpx;
	}

	.info-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.info-label {
		font-size: 26rpx;
		color: var(--text-color-tertiary, #999);
	}

	.info-value {
		font-size: 26rpx;
		color: var(--text-color-secondary, #666);
		background-color: rgba(0, 0, 0, 0.03);
		padding: 4rpx 12rpx;
		border-radius: 20rpx;
		text-align: right;
		word-break: break-all;
	}
</style>
