import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 获取站点列表
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const getDataInfoNew = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/datalist/data/get_data_list' : '/v2/data?action=getData';
    return axios(url, data);
}

/**
 * @description 删除站点
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const deleteSite = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/site?action=DeleteSite' : '/v2/warning?action=delete_list';
    return axios(url, data);
}

/**
 * @description 站点重命名
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const renameSite = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/site?action=site_rname' : '/v2/warning?action=rename_list';
    return axios(url, data);
}

/**
 * @description 修改备注
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const updateRemark = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/data?action=setPs' : '/v2/warning?action=remark_list';
    return axios(url, data);
}

/**
 * @description 获取域名列表
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const getDomainList = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/data?action=getData' : '/v2/data?action=getData';
    return axios(url, data);
}

/**
 * @description 删除域名
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const deleteDomain = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/site?action=DelDomain' : '/v2/site?action=DelDomain';
    return axios(url, data, 'POST', (res) => {
        if (DEFAULT_API_TYPE === 'domestic') {
            return res;
        } else {
            return {
                status: true,
                msg: res.result,
            }
        }
    });
}

/**
 * @description 获取SSL证书信息
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const getSSLInfo = (data) => {
    return axios('/site?action=GetSSL', data, 'POST', (res) => res, {}, 'domestic');
}

/**
 * @description 启动SSL证书
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const setSSLInfo = (data) => {
    return axios('/site?action=SetSSL', data, 'POST', (res) => res, {}, 'domestic');
}

/**
 * @description 关闭SSL证书
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const closeSSL = (data) => {
    return axios('/site?action=CloseSSLConf', data, 'POST', (res) => res, {}, 'domestic');
}

/**
 * @description 开启强制HTTPS
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const setSiteHttps = (data) => {
    return axios('/site?action=HttpToHttps', data, 'POST', (res) => res, {}, 'domestic');
}

/**
 * @description 关闭强制HTTPS
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const closeSiteHttps = (data) => {
    return axios('/site?action=CloseToHttps', data, 'POST', (res) => res, {}, 'domestic');
}

/**
 * @description 获取证书列表
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const getCertList = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/ssl?action=get_cert_list' : '/ssl?action=GetCertList';
    return axios(url, data);
}

/**
 * @description 部署证书
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const deployCert = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/ssl?action=SetBatchCertToSite' : '/ssl?action=SetCertToSite';
    return axios(url, data, 'POST', (res) => {
        try {
            if (DEFAULT_API_TYPE === 'domestic') {
                // 健壮性处理：检查 res 是否为有效对象
                if (!res || typeof res !== 'object') {
                    return {
                        status: false,
                        msg: '接口返回数据格式错误'
                    };
                }

                // 检查 successList 是否存在且为数组
                const hasSuccess = Array.isArray(res.successList) && res.successList.length > 0;

                // 提取错误信息，确保有合理的默认值
                let errorMsg = '部署失败';
                if (Array.isArray(res.faildList) && res.faildList.length > 0) {
                    errorMsg = res.faildList[0]?.error_msg || '未知错误';
                }

                return {
                    status: hasSuccess,
                    msg: hasSuccess ? '部署成功' : errorMsg
                };
            } else {
                // 国际版API直接返回原始响应
                return res || { status: false, msg: '接口返回数据为空' };
            }
        } catch (error) {
            console.error('处理API响应时出错：', error);
            return {
                status: false,
                msg: '处理响应数据时出错: ' + (error.message || '未知错误')
            };
        }
    });
}

/**
 * @description 删除证书
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const deleteCert = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/ssl?action=remove_cloud_cert' : '/ssl?action=RemoveCert';
    return axios(url, data);
}
