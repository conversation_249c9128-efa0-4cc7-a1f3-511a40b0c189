<template>
  <page-container ref="pageContainer" :title="$t('linux.database')">
    <install-database v-if="!isInstallDatabase" />
    <template v-else>
      <!-- 模糊遮罩 -->
      <view class="blur-overlay" v-if="showContextMenu" @click="hideContextMenu"></view>

      <!-- 悬浮上下文菜单 -->
      <view class="context-menu" v-if="showContextMenu" :style="menuPosition" :class="menuPosition.class">
        <view class="menu-item" @click="copyPassword">
          <uni-icons type="paperclip" size="16" color="#007AFF"></uni-icons>
          <text class="menu-text">{{ $t('database.copyPassword') }}</text>
        </view>
        <view class="menu-divider"></view>
        <view class="menu-item" @click="showEditDialog = true">
          <uni-icons type="compose" size="16" color="#007AFF"></uni-icons>
          <text class="menu-text">{{ $t('database.modifyRemark') }}</text>
        </view>
        <view class="menu-divider"></view>
        <view class="menu-item menu-delete" @click="showDeleteDialog = true">
          <uni-icons type="trash" size="16" color="#FF3B30"></uni-icons>
          <text class="menu-text menu-text-delete">{{ $t('common.delete') }}</text>
        </view>
      </view>

      <!-- 克隆项容器 - 放在外层，使用fixed定位 -->
      <view class="fixed-clone-container" v-if="showContextMenu">
        <view class="item-clone-wrapper" :style="clonePosition" v-if="activeDatabase">
          <view class="db-item-clone bg-primary">
            <view class="db-main-info">
              <view class="db-name">{{ activeDatabase.name }}</view>
              <view class="db-username">{{ $t('database.username') }}：{{ activeDatabase.username }}</view>
            </view>
            <view class="db-extra-info">
              <view class="db-location">
                <text class="info-label">{{ $t('database.location') }}:</text>
                <text class="info-value">{{ getPosition(sqlLocationList, 'mysql', activeDatabase.sid) }}</text>
              </view>
              <view class="db-backup">
                <text class="info-label">{{ $t('database.backup') }}:</text>
                <text class="info-value" :class="{ 'backup-exists': activeDatabase.backup_count > 0 }">{{
                  activeDatabase.backup_count > 0 ? $t('database.backupExists', {count: activeDatabase.backup_count}) : $t('database.noBackup')
                }}</text>
              </view>
            </view>
            <view class="flex justify-between items-center">
              <view class="db-remark">{{ $t('database.remark') }}：{{ activeDatabase.ps }}</view>
              <view class="db-remark">
                <text class="info-label">{{ $t('database.password') }}:</text>
                <text class="text-26" :class="[activeDatabase.password ? 'text-#20a50a' : 'info-value']" @click="copyPassword">{{
                  `${activeDatabase.password ? $t('database.copyPassword') : $t('database.noPassword')}`
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <z-paging
        ref="databasePaging"
        class="mt-170"
        :default-page-size="50"
        use-virtual-list
        :force-close-inner-list="true"
        :auto-hide-loading-after-first-loaded="false"
        :auto-show-system-loading="true"
        @query="queryList"
        @virtualListChange="virtualListChange"
        @refresherStatusChange="reload"
        :refresher-complete-delay="200"
      >
        <view class="px-20 mt-20" v-for="(item, index) in databaseList" :id="`zp-id-${item.zp_index}`" :key="item.zp_index">
          <view
            class="db-item-container bg-primary"
            @touchstart="handleTouchStart($event)"
            @touchmove="handleTouchMove($event)"
            @touchend="handleTouchEnd($event)"
            @touchcancel="handleTouchCancel($event)"
            :data-index="index"
            :data-db="JSON.stringify(item)"
          >
            <view class="db-main-info">
              <view class="db-name">{{ item.name }}</view>
              <view class="db-username">{{ $t('database.username') }}：{{ item.username }}</view>
            </view>
            <view class="db-extra-info">
              <view class="db-location">
                <text class="info-label">{{ $t('database.location') }}:</text>
                <text class="info-value">{{ getPosition(sqlLocationList, 'mysql', item.sid) }}</text>
              </view>
              <view class="db-backup">
                <text class="info-label">{{ $t('database.backup') }}:</text>
                <text class="info-value" :class="{ 'backup-exists': item.backup_count > 0 }">{{
                  item.backup_count > 0 ? $t('database.backupExists', {count: item.backup_count}) : $t('database.noBackup')
                }}</text>
              </view>
            </view>
            <view class="flex justify-between items-center">
              <view class="db-remark">{{ $t('database.remark') }}：{{ item.ps }}</view>
              <view class="db-remark">
                <text class="info-label">{{ $t('database.password') }}:</text>
                <text class="text-26" :class="[item.password ? 'text-#20a50a' : 'info-value']" @click="copyPasswordItem(item.password)">{{
                  `${item.password ? $t('database.copyPassword') : $t('database.noPassword')}`
                }}</text>
              </view>
            </view>
          </view>
        </view>
      </z-paging>

      <!-- 编辑备注对话框 -->
      <CustomDialog
        contentHeight="100rpx"
        v-model="showEditDialog"
        :title="$t('database.modifyRemark')"
        :confirmText="$t('common.modify')"
        @confirm="confirmEdit"
        @cancel="editRemark = ''"
      >
        <view class="text-secondary flex justify-center items-center h-full">
          <uv-input v-model="editRemark" :placeholder="activeDatabase?.ps" />
        </view>
      </CustomDialog>

      <!-- 删除确认对话框 -->
      <CustomDialog
        contentHeight="200rpx"
        v-model="showDeleteDialog"
        :title="$t('website.warning')"
        :confirmText="$t('common.delete')"
        :confirmStyle="{
          backgroundColor: '#FF3B30',
        }"
        @confirm="confirmDelete"
      >
        <view class="text-secondary flex justify-center items-center h-full">
          {{ $t('database.confirmDelete', {name: activeDatabase?.name}) }}
        </view>
      </CustomDialog>
    </template>
  </page-container>
</template>

<script setup>
  import { onShow, onBackPress, onLoad, onUnload } from '@dcloudio/uni-app';
  import PageContainer from '@/components/PageContainer/index.vue';
  import InstallDatabase from './install.vue';
  import CustomDialog from '@/components/CustomDialog/index.vue';
  import { $t } from '@/locale';
  import {
    isInstallDatabase,
    getDataBaseStatus,
    databasePaging,
    databaseList,
    showContextMenu,
    activeDatabase,
    menuPosition,
    clonePosition,
    showEditDialog,
    showDeleteDialog,
    editRemark,
    queryList,
    virtualListChange,
    handleTouchStart,
    handleTouchMove,
    handleTouchEnd,
    handleTouchCancel,
    hideContextMenu,
    copyPassword,
    confirmEdit,
    confirmDelete,
    pageContainer,
    getServerList,
    sqlLocationList,
    getPosition,
  } from './useController';

  const reload = (reloadType) => {
    if (reloadType === 'complete') {
      pageContainer.value.notify.success($t('common.refreshSuccess'));
    }
  };

  // 复制密码
  const copyPasswordItem = (password) => {
    if (!password) return;

    uni.setClipboardData({
      data: password,
      success: () => {
        uni.showToast({
          title: $t('database.passwordCopied'),
          icon: 'success',
        });
      },
    });
  };

  onShow(() => {
    getDataBaseStatus();
  });

  onBackPress(() => {
    // 如果长按菜单正在显示，先关闭菜单
    if (showContextMenu.value) {
      hideContextMenu();
      return true;
    }
    return false;
  });

  onLoad(() => {
    getServerList();
  });

  onUnload(() => {
    // 卸载时关闭所有对话框
    showEditDialog.value = false;
    showDeleteDialog.value = false;
  });
</script>

<style lang="scss" scoped>
  /* 模糊遮罩 */
  .blur-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
    z-index: 15;
    pointer-events: auto;
    touch-action: none; /* 禁止所有触摸操作 */
    user-select: none; /* 禁止选择 */
  }

  /* 数据库项样式 */
  .db-item-container {
    padding: 30rpx;
    border-radius: 14rpx;
    border: 1rpx solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
  }

  .db-item-container:active {
    background-color: rgba(var(--bg-color-secondary-rgb), 0.4);
    transform: scale(0.98);
  }

  .db-main-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16rpx;
    padding-bottom: 12rpx;
    border-bottom: 1px solid var(--border-color);
  }

  .db-extra-info {
    display: flex;
    justify-content: space-between;
    padding-bottom: 12rpx;
  }

  .db-location,
  .db-backup {
    display: flex;
    align-items: center;
  }

  .info-label {
    font-size: 26rpx;
    color: var(--text-color-tertiary, #999);
    margin-right: 8rpx;
  }

  .info-value {
    font-size: 26rpx;
    color: var(--text-color-secondary, #666);
    background-color: rgba(0, 0, 0, 0.03);
    padding: 4rpx 12rpx;
    border-radius: 20rpx;
  }

  .backup-exists {
    background-color: rgba(39, 174, 96, 0.15);
    color: #27ae60;
  }

  .db-name {
    font-size: 30rpx;
    font-weight: bold;
    color: var(--text-color-primary, #333);
    max-width: 40%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .db-username {
    font-size: 28rpx;
    color: var(--text-color-secondary, #666);
    max-width: 60%;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .db-remark {
    font-size: 26rpx;
    color: var(--text-color-tertiary, #999);
    line-height: 1.4;
  }

  /* 克隆项样式 */
  .fixed-clone-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 901;
  }

  .item-clone-wrapper {
    position: absolute;
    z-index: 902;
  }

  .db-item-clone {
    padding: 30rpx;
    border-radius: 14rpx;
    box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  }

  /* 上下文菜单样式 */
  .context-menu {
    position: fixed;
    z-index: 999;
    background-color: #ffffff;
    border-radius: 14rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);
    min-width: 220rpx;
    transform: translateX(-50%);
    padding: 10rpx 0;
  }

  .menu-item {
    display: flex;
    align-items: center;
    padding: 24rpx 32rpx;
  }

  .menu-text {
    margin-left: 16rpx;
    font-size: 28rpx;
    color: #333;
  }

  .menu-divider {
    height: 2rpx;
    background-color: #f2f2f2;
    margin: 0 10rpx;
  }

  .menu-delete .menu-text-delete {
    color: #ff3b30;
  }

  /* 菜单位置类 */
  .menu-top {
    transform: translateX(-50%);
  }

  .menu-bottom {
    transform: translateX(-50%);
  }
</style>
