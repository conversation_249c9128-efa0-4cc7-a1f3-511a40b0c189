import axios from '@/api/request';

const DEFAULT_API_TYPE = import.meta.env.VITE_PANEL_OWNER || 'domestic';

/**
 * @description 列表查询 - mysql
 * @param {string} data.table 查询表名
 * @param {string} data.search 查询条件
 * @param {string} data.limit 分页参数
 * @param {string} data.p 分页参数
 * @param {string} data.order 排序参数
 */
export const getMysqlList = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/datalist/data/get_data_list' : '/v2/data?action=getData';
    return axios(url, data);
}

/**
 * @description 列表查询是否配置远程数据库-mysql
 * @param {string} type 查询列表类型
 */
export const getMysqlCloudServer = () => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/database?action=GetCloudServer' : '/v2/database?action=GetCloudServer';
    return axios(url);
}

/**
 * @description 删除数据库
 * @param {number} data.id 数据库id
 * @param {string} data.name 数据库名称
 */
export const deleteDatabase = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/database?action=DeleteDatabase' : '/v2/database?action=DeleteDatabase';
    return axios(url, data, 'POST', (res) => {
        if (DEFAULT_API_TYPE === 'domestic') {
            return res;
        } else {
            return {
                status: res?.result,
                msg: res.result,
            }
        }
    });
}

/**
 * @description 修改备注
 * @param { AnyObject } data
 * @returns { Promise }
 */
export const updateRemark = (data) => {
    const url = DEFAULT_API_TYPE === 'domestic' ? '/data?action=setPs' : '/v2/data?action=setPs';
    return axios(url, data, 'POST', (res) => {
        if (DEFAULT_API_TYPE === 'domestic') {
            return res;
        } else {
            return {
                status: res?.result,
                msg: res.result,
            }
        }
    });
}