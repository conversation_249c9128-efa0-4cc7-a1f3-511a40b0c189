import { ref } from 'vue';
import { getServerList } from '@/api/serverList';
import { getPublicConfig, setDebug } from '@/api/config';
import { getPingInfo, setPing } from '@/api/firewall';
import { isObject } from '@/utils/type';

export const developerMode = ref(false);
export const panelStatusInfo = ref({});
export const safeConfig = ref({});
export const pageContainer = ref(null);
export const disablePing = ref(false);
// 临时保存原始状态
export const originalPingState = ref(false);
export const originalDeveloperState = ref(false);

export const handleSave = (setting) => {
    uni.showToast({
        title: `${setting}设置成功`,
        icon: 'success',
    });
};

// 获取面板信息
export const getPanelInfo = async () => {
    try {
        const res = await getServerList();
        panelStatusInfo.value = res;
        const publicConfig = await getPublicConfig();
        developerMode.value = publicConfig.debug === 'checked';
        if (isObject(publicConfig?.panel)) {
            safeConfig.value = publicConfig.panel;
        }
    } catch (error) {
        console.log(error);
    }
}

// 获取防火墙信息
export const getFirewallInfo = async () => {
    try {
        const res = await getPingInfo();
        disablePing.value = !res.ping
    } catch (error) {
        console.log(error);
    }
}

// 取消禁PING
export const cancelPing = () => {
    originalPingState.value = false;
    setTimeout(() => {
        disablePing.value = !disablePing.value;
    }, 300);
}

// 取消开发者模式
export const cancelDeveloper = () => {
    originalDeveloperState.value = false;
    setTimeout(() => {
        developerMode.value = !developerMode.value;
    }, 300);
}

// 确认开发者模式
export const confirmDeveloper = async (close) => {
    try {
        const res = await setDebug();
        if (res.status) {
            close && close();
        }
        res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg)
    } catch (error) {
        console.log(error);
    }
}

// 确认禁PING
export const confirmPing = async (close) => {
    try {
        const res = await setPing({ status: disablePing.value ? 0 : 1 });
        if (res.status) {
            close && close();
        }
        res.status ? pageContainer.value.notify.success(res.msg) : pageContainer.value.notify.error(res.msg)
    } catch (error) {
        console.log(error);
    }
}


